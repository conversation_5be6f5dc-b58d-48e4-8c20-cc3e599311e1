package main

import (
	"fmt"
	"log"
	"simple_inventory_management_system/mcp"
)

func main() {
	// 创建MCP客户端
	client := mcp.NewMCPClient("http://localhost:3000/mcp")
	
	fmt.Printf("会话ID: %s\n", client.SessionID)
	fmt.Printf("MCP服务地址: %s\n", client.BaseURL)
	
	// 测试连接
	fmt.Println("正在测试MCP服务连接...")
	err := client.TestConnection()
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}
	
	fmt.Println("✅ MCP服务连接成功！")
	
	// 执行搜索测试
	fmt.Println("正在执行搜索测试...")
	results, err := client.Search("Go语言教程", 3, []string{"bing"})
	if err != nil {
		log.Printf("搜索失败: %v", err)
		return
	}
	
	fmt.Printf("搜索结果数量: %d\n", len(results))
	for i, result := range results {
		fmt.Printf("结果 %d:\n", i+1)
		fmt.Printf("  标题: %s\n", result.Title)
		fmt.Printf("  URL: %s\n", result.URL)
		fmt.Printf("  描述: %s\n", result.Description)
		fmt.Printf("  来源: %s\n", result.Source)
		fmt.Printf("  引擎: %s\n", result.Engine)
		fmt.Println()
	}
}
