package mcp

import (
	"bytes"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// MCPClient MCP客户端
type MCPClient struct {
	BaseURL     string
	Client      *http.Client
	SessionID   string
	initialized bool
}

// SearchRequest 搜索请求结构
type SearchRequest struct {
	Query   string   `json:"query"`
	Limit   int      `json:"limit,omitempty"`
	Engines []string `json:"engines,omitempty"`
}

// SearchResult 搜索结果结构
type SearchResult struct {
	Title       string `json:"title"`
	URL         string `json:"url"`
	Description string `json:"description"`
	Source      string `json:"source"`
	Engine      string `json:"engine"`
}

// FetchArticleRequest 获取文章请求结构
type FetchArticleRequest struct {
	URL string `json:"url"`
}

// FetchArticleResult 获取文章结果结构
type FetchArticleResult struct {
	Content string `json:"content"`
}

// MCPToolRequest MCP工具请求结构
type MCPToolRequest struct {
	JSONRPC string    `json:"jsonrpc"`
	ID      int       `json:"id"`
	Method  string    `json:"method"`
	Params  MCPParams `json:"params"`
}

// MCPParams MCP参数结构
type MCPParams struct {
	Name      string      `json:"name"`
	Arguments interface{} `json:"arguments"`
}

// MCPResponse MCP响应结构
type MCPResponse struct {
	Result interface{} `json:"result,omitempty"`
	Error  *MCPError   `json:"error,omitempty"`
}

// MCPError MCP错误结构
type MCPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// generateSessionID 生成会话ID (UUID格式)
func generateSessionID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	// 设置版本号和变体
	bytes[6] = (bytes[6] & 0x0f) | 0x40 // Version 4
	bytes[8] = (bytes[8] & 0x3f) | 0x80 // Variant 10
	return fmt.Sprintf("%x-%x-%x-%x-%x", bytes[0:4], bytes[4:6], bytes[6:8], bytes[8:10], bytes[10:16])
}

// NewMCPClient 创建新的MCP客户端
func NewMCPClient(baseURL string) *MCPClient {
	client := &MCPClient{
		BaseURL:   baseURL,
		SessionID: generateSessionID(),
		Client: &http.Client{
			Timeout: 30 * time.Second,
		},
		initialized: true, // 暂时跳过初始化
	}

	return client
}

// initializeSession 初始化MCP会话
func (c *MCPClient) initializeSession() error {
	// 发送初始化请求
	initRequest := map[string]interface{}{
		"jsonrpc": "2.0",
		"id":      1,
		"method":  "initialize",
		"params": map[string]interface{}{
			"protocolVersion": "2024-11-05",
			"capabilities": map[string]interface{}{
				"tools": map[string]interface{}{},
			},
			"clientInfo": map[string]interface{}{
				"name":    "simple-inventory-client",
				"version": "1.0.0",
			},
		},
	}

	jsonData, err := json.Marshal(initRequest)
	if err != nil {
		return fmt.Errorf("序列化初始化请求失败: %v", err)
	}

	req, err := http.NewRequest("POST", c.BaseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建初始化请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("mcp-session-id", c.SessionID)

	resp, err := c.Client.Do(req)
	if err != nil {
		return fmt.Errorf("发送初始化请求失败: %v", err)
	}
	defer resp.Body.Close()

	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取初始化响应失败: %v", err)
	}

	if resp.StatusCode == http.StatusOK {
		c.initialized = true
	}

	return nil
}

// Search 执行网络搜索
func (c *MCPClient) Search(query string, limit int, engines []string) ([]SearchResult, error) {
	if limit <= 0 {
		limit = 5
	}
	if engines == nil {
		// engines = []string{"bing", "baidu"}
		engines = []string{"bing"}
	}

	request := MCPToolRequest{
		JSONRPC: "2.0",
		ID:      1,
		Method:  "tools/call",
		Params: MCPParams{
			Name: "search",
			Arguments: SearchRequest{
				Query:   query,
				Limit:   limit,
				Engines: engines,
			},
		},
	}

	response, err := c.callTool(request)
	if err != nil {
		return nil, err
	}

	// 解析搜索结果
	var results []SearchResult
	if response.Result != nil {
		resultBytes, err := json.Marshal(response.Result)
		if err != nil {
			return nil, fmt.Errorf("解析搜索结果失败: %v", err)
		}

		if err := json.Unmarshal(resultBytes, &results); err != nil {
			return nil, fmt.Errorf("反序列化搜索结果失败: %v", err)
		}
	}

	return results, nil
}

// FetchCSDNArticle 获取CSDN文章内容
func (c *MCPClient) FetchCSDNArticle(url string) (*FetchArticleResult, error) {
	request := MCPToolRequest{
		JSONRPC: "2.0",
		ID:      2,
		Method:  "tools/call",
		Params: MCPParams{
			Name: "fetchCsdnArticle",
			Arguments: FetchArticleRequest{
				URL: url,
			},
		},
	}

	response, err := c.callTool(request)
	if err != nil {
		return nil, err
	}

	// 解析文章内容
	var result FetchArticleResult
	if response.Result != nil {
		resultBytes, err := json.Marshal(response.Result)
		if err != nil {
			return nil, fmt.Errorf("解析文章内容失败: %v", err)
		}

		if err := json.Unmarshal(resultBytes, &result); err != nil {
			return nil, fmt.Errorf("反序列化文章内容失败: %v", err)
		}
	}

	return &result, nil
}

// FetchLinuxDoArticle 获取Linux.do文章内容
func (c *MCPClient) FetchLinuxDoArticle(url string) (*FetchArticleResult, error) {
	request := MCPToolRequest{
		JSONRPC: "2.0",
		ID:      3,
		Method:  "tools/call",
		Params: MCPParams{
			Name: "fetchLinuxDoArticle",
			Arguments: FetchArticleRequest{
				URL: url,
			},
		},
	}

	response, err := c.callTool(request)
	if err != nil {
		return nil, err
	}

	// 解析文章内容
	var result FetchArticleResult
	if response.Result != nil {
		resultBytes, err := json.Marshal(response.Result)
		if err != nil {
			return nil, fmt.Errorf("解析文章内容失败: %v", err)
		}

		if err := json.Unmarshal(resultBytes, &result); err != nil {
			return nil, fmt.Errorf("反序列化文章内容失败: %v", err)
		}
	}

	return &result, nil
}

// callTool 调用MCP工具
func (c *MCPClient) callTool(request MCPToolRequest) (*MCPResponse, error) {
	// 确保会话已初始化
	if !c.initialized {
		if err := c.initializeSession(); err != nil {
			return nil, fmt.Errorf("初始化会话失败: %v", err)
		}
	}
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	req, err := http.NewRequest("POST", c.BaseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("mcp-session-id", c.SessionID)

	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("MCP请求失败 (状态码: %d): %s", resp.StatusCode, string(body))
	}

	var mcpResponse MCPResponse
	if err := json.Unmarshal(body, &mcpResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v, 原始响应: %s", err, string(body))
	}

	if mcpResponse.Error != nil {
		return nil, fmt.Errorf("MCP错误: %s", mcpResponse.Error.Message)
	}

	return &mcpResponse, nil
}

// UpdateBaseURL 更新MCP服务地址
func (c *MCPClient) UpdateBaseURL(baseURL string) {
	c.BaseURL = baseURL
}

// TestConnection 测试MCP服务连接
func (c *MCPClient) TestConnection() error {
	// 尝试执行一个简单的搜索来测试连接
	_, err := c.Search("test", 1, []string{"bing"})
	return err
}

// IsAvailable 检查MCP服务是否可用
func (c *MCPClient) IsAvailable() bool {
	// 尝试访问健康检查端点
	healthURL := strings.Replace(c.BaseURL, "/mcp", "/health", 1)
	req, err := http.NewRequest("GET", healthURL, nil)
	if err != nil {
		return false
	}

	resp, err := c.Client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	// 健康检查端点应该返回200
	if resp.StatusCode == http.StatusOK {
		return true
	}

	// 如果健康检查失败，尝试直接访问MCP端点
	// MCP端点可能返回405 Method Not Allowed，这也表示服务可用
	req2, err := http.NewRequest("GET", c.BaseURL, nil)
	if err != nil {
		return false
	}

	resp2, err := c.Client.Do(req2)
	if err != nil {
		return false
	}
	defer resp2.Body.Close()

	// 对于MCP端点，405 Method Not Allowed 也表示服务可用
	return resp2.StatusCode == http.StatusOK || resp2.StatusCode == http.StatusMethodNotAllowed
}
