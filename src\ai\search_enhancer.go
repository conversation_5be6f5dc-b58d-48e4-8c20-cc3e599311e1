package ai

import (
	"fmt"
	"regexp"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"
	"simple_inventory_management_system/mcp"
	"strings"

	"github.com/sirupsen/logrus"
)

// SearchEnhancer 搜索增强器
type SearchEnhancer struct {
	mcpClient *mcp.MCPClient
}

// NewSearchEnhancer 创建新的搜索增强器
func NewSearchEnhancer() *SearchEnhancer {
	// 从配置中获取MCP服务地址
	mcpURL, err := database.GetConfig("mcp_websearch_url")
	if err != nil || mcpURL == "" {
		mcpURL = "http://localhost:3000/mcp" // 默认地址
	}

	return &SearchEnhancer{
		mcpClient: mcp.NewMCPClient(mcpURL),
	}
}

// EnhanceMessage 增强消息，如果需要搜索则添加搜索结果
func (se *SearchEnhancer) EnhanceMessage(message string) string {
	logger.Log.WithFields(logrus.Fields{
		"message": message,
	}).Info("检查消息是否需要搜索")

	// 检查是否需要搜索
	if !se.needsSearch(message) {
		logger.Log.Info("消息不需要搜索，跳过搜索增强")
		return message
	}

	logger.Log.Info("消息需要搜索，开始搜索增强")

	// 检查MCP服务是否可用
	if !se.mcpClient.IsAvailable() {
		logger.Log.Warn("MCP搜索服务不可用，跳过搜索增强")
		return message
	}

	// 提取搜索关键词
	searchQuery := se.extractSearchQuery(message)
	if searchQuery == "" {
		return message
	}

	logger.Log.WithFields(logrus.Fields{
		"original_message": message,
		"search_query":     searchQuery,
	}).Info("执行搜索增强")

	// 执行搜索
	searchResults, err := se.mcpClient.Search(searchQuery, 5, []string{"bing", "baidu"})
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
			"query": searchQuery,
		}).Error("搜索失败")
		return message
	}

	// 如果没有搜索结果，返回原消息
	if len(searchResults) == 0 {
		return message
	}

	// 构建增强后的消息
	enhancedMessage := se.buildEnhancedMessage(message, searchQuery, searchResults)

	logger.Log.WithFields(logrus.Fields{
		"results_count":   len(searchResults),
		"enhanced_length": len(enhancedMessage),
	}).Info("搜索增强完成")

	return enhancedMessage
}

// needsSearch 判断是否需要搜索
func (se *SearchEnhancer) needsSearch(message string) bool {
	originalMessage := message
	message = strings.ToLower(message)

	logger.Log.WithFields(logrus.Fields{
		"original_message":  originalMessage,
		"lowercase_message": message,
	}).Info("检查消息是否包含搜索触发关键词")

	// 搜索触发关键词
	searchTriggers := []string{
		"搜索", "查找", "找一下", "搜一下", "查询",
		"最新", "新闻", "资讯", "动态", "趋势",
		"什么是", "如何", "怎么", "为什么",
		"教程", "方法", "步骤", "指南",
		"比较", "对比", "区别", "差异",
		"推荐", "建议", "选择", "哪个好",
		"价格", "报价", "多少钱", "费用",
		"评测", "评价", "口碑", "怎么样",
		"天气", "气温", "温度", "下雨", "晴天", "阴天", "雾霾",
		"今天", "明天", "昨天", "现在", "当前", "实时",
		"search", "find", "lookup", "what is", "how to",
	}

	for _, trigger := range searchTriggers {
		if strings.Contains(message, trigger) {
			logger.Log.WithFields(logrus.Fields{
				"trigger": trigger,
				"message": originalMessage,
			}).Info("找到搜索触发关键词")
			return true
		}
	}

	// 检查是否包含问号（可能是问题）
	if strings.Contains(message, "？") || strings.Contains(message, "?") {
		logger.Log.WithFields(logrus.Fields{
			"message": originalMessage,
		}).Info("消息包含问号，触发搜索")
		return true
	}

	logger.Log.WithFields(logrus.Fields{
		"message": originalMessage,
	}).Info("消息不包含搜索触发条件")
	return false
}

// extractSearchQuery 提取搜索关键词
func (se *SearchEnhancer) extractSearchQuery(message string) string {
	// 移除常见的停用词和触发词
	stopWords := []string{
		"请", "帮我", "帮忙", "可以", "能否", "能不能",
		"搜索", "查找", "找一下", "搜一下", "查询",
		"一下", "看看", "了解", "知道",
		"please", "help", "can you", "could you",
		"search", "find", "lookup",
	}

	query := message
	for _, word := range stopWords {
		query = strings.ReplaceAll(query, word, " ")
	}

	// 清理多余的空格
	query = regexp.MustCompile(`\s+`).ReplaceAllString(query, " ")
	query = strings.TrimSpace(query)

	// 如果查询太短，使用原始消息的关键部分
	if len(query) < 3 {
		query = message
	}

	// 限制查询长度
	if len(query) > 100 {
		query = query[:100]
	}

	return query
}

// buildEnhancedMessage 构建增强后的消息
func (se *SearchEnhancer) buildEnhancedMessage(originalMessage, searchQuery string, results []mcp.SearchResult) string {
	var builder strings.Builder

	builder.WriteString(originalMessage)
	builder.WriteString("\n\n")
	builder.WriteString("🔍 **相关搜索结果：**\n")
	builder.WriteString(fmt.Sprintf("搜索关键词：%s\n\n", searchQuery))

	for i, result := range results {
		if i >= 5 { // 最多显示5个结果
			break
		}

		builder.WriteString(fmt.Sprintf("**%d. %s**\n", i+1, result.Title))
		builder.WriteString(fmt.Sprintf("来源：%s (%s)\n", result.Source, result.Engine))
		builder.WriteString(fmt.Sprintf("链接：%s\n", result.URL))

		if result.Description != "" {
			// 限制描述长度
			description := result.Description
			if len(description) > 200 {
				description = description[:200] + "..."
			}
			builder.WriteString(fmt.Sprintf("摘要：%s\n", description))
		}
		builder.WriteString("\n")
	}

	builder.WriteString("---\n")
	builder.WriteString("💡 **请基于以上搜索结果和您的知识来回答用户的问题。**")

	return builder.String()
}

// SearchAndFetchArticle 搜索并获取文章详细内容
func (se *SearchEnhancer) SearchAndFetchArticle(query string, preferredSources []string) (string, error) {
	// 执行搜索
	results, err := se.mcpClient.Search(query, 10, []string{"bing", "baidu", "csdn"})
	if err != nil {
		return "", fmt.Errorf("搜索失败: %v", err)
	}

	if len(results) == 0 {
		return "", fmt.Errorf("未找到相关结果")
	}

	// 查找首选来源的文章
	var targetResult *mcp.SearchResult
	for _, result := range results {
		for _, source := range preferredSources {
			if strings.Contains(strings.ToLower(result.Source), strings.ToLower(source)) ||
				strings.Contains(strings.ToLower(result.URL), strings.ToLower(source)) {
				targetResult = &result
				break
			}
		}
		if targetResult != nil {
			break
		}
	}

	// 如果没有找到首选来源，使用第一个结果
	if targetResult == nil {
		targetResult = &results[0]
	}

	// 根据来源获取文章内容
	var articleContent string
	if strings.Contains(strings.ToLower(targetResult.URL), "csdn.net") {
		article, err := se.mcpClient.FetchCSDNArticle(targetResult.URL)
		if err != nil {
			return "", fmt.Errorf("获取CSDN文章失败: %v", err)
		}
		articleContent = article.Content
	} else if strings.Contains(strings.ToLower(targetResult.URL), "linux.do") {
		article, err := se.mcpClient.FetchLinuxDoArticle(targetResult.URL)
		if err != nil {
			return "", fmt.Errorf("获取Linux.do文章失败: %v", err)
		}
		articleContent = article.Content
	} else {
		// 对于其他来源，只返回搜索结果
		articleContent = fmt.Sprintf("标题：%s\n链接：%s\n摘要：%s",
			targetResult.Title, targetResult.URL, targetResult.Description)
	}

	// 构建完整的响应
	var builder strings.Builder
	builder.WriteString(fmt.Sprintf("🔍 **搜索结果：%s**\n\n", query))
	builder.WriteString(fmt.Sprintf("**来源：** %s (%s)\n", targetResult.Source, targetResult.Engine))
	builder.WriteString(fmt.Sprintf("**链接：** %s\n\n", targetResult.URL))
	builder.WriteString("**内容：**\n")
	builder.WriteString(articleContent)

	return builder.String(), nil
}

// IsEnabled 检查搜索增强是否启用
func (se *SearchEnhancer) IsEnabled() bool {
	enabled, err := database.GetConfig("enable_web_search")
	if err != nil {
		return false
	}
	return enabled == "true"
}

// TestConnection 测试MCP服务连接
func (se *SearchEnhancer) TestConnection() error {
	if se.mcpClient == nil {
		return fmt.Errorf("MCP客户端未初始化")
	}
	return se.mcpClient.TestConnection()
}

// UpdateMCPURL 更新MCP服务地址
func (se *SearchEnhancer) UpdateMCPURL(url string) {
	if se.mcpClient != nil {
		se.mcpClient.UpdateBaseURL(url)
	}
}

// GetMCPURL 获取当前MCP服务地址
func (se *SearchEnhancer) GetMCPURL() string {
	return se.mcpClient.BaseURL
}
